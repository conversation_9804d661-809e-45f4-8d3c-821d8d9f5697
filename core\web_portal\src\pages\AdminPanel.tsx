import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Users, Server, AlertTriangle, Activity, CheckCircle, XCircle, Clock, Settings, RefreshCw, Cpu, HardDrive, MemoryStick, Mail, Plus, X } from 'lucide-react';
import Layout from '../components/common/Layout';
import Card from '../components/common/Card';
import toast from 'react-hot-toast';
// import ConnectionStatus from '../components/common/ConnectionStatus';

import {
  adminApi,
  type SystemHealth,
  type ErrorTrend,
  type TenantActivity,
  type ProcessingQueue,
  type ErrorLog
} from '../services/api';

// Type for notification recipients
interface NotificationRecipient {
  name: string;
  email: string;
}
import { useApp } from '../contexts/AppContext';

const AdminPanel: React.FC = () => {
  const { isDevelopmentMode, toggleDevelopmentMode } = useApp();
  const [activeTab, setActiveTab] = useState<'overview' | 'tenants' | 'config' | 'system' | 'logs'>('overview');

  // Reset to overview tab if user switches to production mode while on config tab
  useEffect(() => {
    if (!isDevelopmentMode && activeTab === 'config') {
      setActiveTab('overview');
    }
  }, [isDevelopmentMode, activeTab]);

  // Live data state
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [errorTrends, setErrorTrends] = useState<ErrorTrend[]>([]);
  const [tenantActivity, setTenantActivity] = useState<TenantActivity[]>([]);
  const [processingQueue, setProcessingQueue] = useState<ProcessingQueue | null>(null);
  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [configData, setConfigData] = useState<any>(null);

  // Configuration modal states
  const [showAddMailboxModal, setShowAddMailboxModal] = useState(false);
  const [showAddDocTypeModal, setShowAddDocTypeModal] = useState(false);
  const [showEditConfigModal, setShowEditConfigModal] = useState(false);
  const [editingConfigSection, setEditingConfigSection] = useState<string>('');
  const [newMailbox, setNewMailbox] = useState({ email: '', displayName: '', enabled: true });
  const [newDocType, setNewDocType] = useState({
    name: '',
    keywords: '',
    upload: true,
    notify: false,
    storagePath: '',
    recipients: [{ name: '', email: '' }]
  });
  const [editingConfig, setEditingConfig] = useState<any>(null);

  // New recipient form states
  const [newRecipientName, setNewRecipientName] = useState('');
  const [newRecipientEmail, setNewRecipientEmail] = useState('');

  const fetchConfigData = async () => {
    try {
      const response = await fetch(`/api/config/load?dev_mode=${isDevelopmentMode}`);
      if (response.ok) {
        const config = await response.json();
        setConfigData(config);
      } else {
        console.error('Failed to load config data');
        // Set default empty config
        setConfigData({
          mailboxes: {},
          document_types: {},
          defaults: {
            storage: { subfolder_format: '{doc_type}/{document_year}/{company_name}' },
            notification: { recipients: [] },
            preferred_language: 'English'
          }
        });
      }
    } catch (error) {
      console.error('Error fetching config data:', error);
      // Set default empty config on error
      setConfigData({
        mailboxes: {},
        document_types: {},
        defaults: {
          storage: { subfolder_format: '{doc_type}/{document_year}/{company_name}' },
          notification: { recipients: [] },
          preferred_language: 'English'
        }
      });
    }
  };

  // Configuration handlers
  const handleAddMailbox = async () => {
    try {
      const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mailboxes: {
            ...configData?.mailboxes,
            [newMailbox.email]: {
              display_name: newMailbox.displayName,
              enabled: newMailbox.enabled
            }
          }
        }),
      });

      if (response.ok) {
        toast.success(`Mailbox ${newMailbox.email} added successfully`);
        setNewMailbox({ email: '', displayName: '', enabled: true });
        setShowAddMailboxModal(false);
        // Refresh configuration data
        fetchConfigData();
      } else {
        const error = await response.json();
        toast.error(`Failed to add mailbox: ${error.error}`);
      }
    } catch (error) {
      console.error('Error adding mailbox:', error);
      toast.error('Failed to add mailbox');
    }
  };

  const handleAddDocType = async () => {
    try {
      const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_types: {
            ...configData?.document_types,
            [newDocType.name]: {
              keywords: newDocType.keywords.split(',').map(k => k.trim()),
              actions: {
                upload: newDocType.upload,
                notify: newDocType.notify
              },
              ...(newDocType.upload && newDocType.storagePath && {
                storage: {
                  subfolder_format: newDocType.storagePath
                }
              }),
              ...(newDocType.notify && newDocType.recipients.some(r => r.email) && {
                notification: {
                  recipients: newDocType.recipients.filter(r => r.email && r.name)
                }
              })
            }
          }
        }),
      });

      if (response.ok) {
        toast.success(`Document type ${newDocType.name} added successfully`);
        setNewDocType({
          name: '',
          keywords: '',
          upload: true,
          notify: false,
          storagePath: '',
          recipients: [{ name: '', email: '' }]
        });
        setShowAddDocTypeModal(false);
        // Refresh configuration data
        fetchConfigData();
      } else {
        const error = await response.json();
        toast.error(`Failed to add document type: ${error.error}`);
      }
    } catch (error) {
      console.error('Error adding document type:', error);
      toast.error('Failed to add document type');
    }
  };

  const handleSaveConfigChanges = async () => {
    try {
      if (!editingConfig) return;

      let configToSave;

      if (editingConfigSection === 'document_type') {
        // For document type editing, merge changes into the full config
        configToSave = {
          ...configData,
          document_types: {
            ...configData.document_types,
            [editingConfig.document_type]: editingConfig.config
          }
        };
      } else {
        // For other sections (defaults, mailboxes), use the editing config directly
        configToSave = editingConfig;
      }

      const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configToSave),
      });

      if (response.ok) {
        toast.success('Configuration saved successfully');
        setShowEditConfigModal(false);
        setEditingConfig(null);
        setEditingConfigSection('');
        // Refresh configuration data
        fetchConfigData();
      } else {
        const error = await response.json();
        toast.error(`Failed to save configuration: ${error.error}`);
      }
    } catch (error) {
      console.error('Error saving configuration:', error);
      toast.error('Failed to save configuration');
    }
  };

  // Fetch live admin data
  const fetchAdminData = async () => {
    try {
      setIsLoading(true);

      const [systemHealthRes, errorTrendsRes, tenantActivityRes, processingQueueRes, errorLogsRes] = await Promise.all([
        adminApi.getSystemHealth(isDevelopmentMode),
        adminApi.getErrorTrends(isDevelopmentMode),
        adminApi.getTenantActivity(isDevelopmentMode),
        adminApi.getProcessingQueue(isDevelopmentMode),
        adminApi.getErrorLogs(isDevelopmentMode)
      ]);

      setSystemHealth(systemHealthRes);
      setErrorTrends(errorTrendsRes);
      setTenantActivity(tenantActivityRes);
      setProcessingQueue(processingQueueRes);
      setErrorLogs(errorLogsRes);
      setIsConnected(true);
      setLastUpdated(new Date());

    } catch (error) {
      console.error('❌ Failed to fetch admin data:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh data every 30 seconds and when development mode changes
  useEffect(() => {
    fetchAdminData();
    fetchConfigData();

    const interval = setInterval(fetchAdminData, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isDevelopmentMode]);

  // Use only live system health data - no fallback to mock data

  const systemMetrics = systemHealth ? [
    {
      title: 'Active Tenants',
      value: systemHealth.active_tenants.toString(),
      icon: <Users className="h-8 w-8 text-blue-600" />,
      status: 'healthy'
    },
    {
      title: 'System Uptime',
      value: `${Math.round(systemHealth.uptime_hours)}h`,
      icon: <Server className="h-8 w-8 text-green-600" />,
      status: 'healthy'
    },
    {
      title: 'CPU Usage',
      value: `${systemHealth.cpu_usage.toFixed(1)}%`,
      icon: <Cpu className="h-8 w-8 text-orange-600" />,
      status: systemHealth.cpu_usage > 80 ? 'critical' : 'healthy'
    },
    {
      title: 'Memory Usage',
      value: `${systemHealth.memory_usage.toFixed(1)}%`,
      icon: <MemoryStick className="h-8 w-8 text-purple-600" />,
      status: systemHealth.memory_usage > 80 ? 'critical' : 'healthy'
    },
    {
      title: 'Disk Usage',
      value: `${systemHealth.disk_usage.toFixed(1)}%`,
      icon: <HardDrive className="h-8 w-8 text-indigo-600" />,
      status: systemHealth.disk_usage > 90 ? 'critical' : 'healthy'
    },
    {
      title: 'Error Rate',
      value: `${systemHealth.error_rate.toFixed(1)}%`,
      icon: <AlertTriangle className="h-8 w-8 text-red-600" />,
      status: systemHealth.error_rate > 10 ? 'critical' : systemHealth.error_rate > 5 ? 'warning' : 'healthy'
    }
  ] : [];

  // Use only live tenant activity data - no fallback to mock data
  const tenantActivityData = tenantActivity.map(tenant => ({
    name: tenant.tenant_name,
    documents: tenant.documents_this_week,
    mailboxes: tenant.mailbox_count,
    success_rate: tenant.success_rate
  }));

  // Use only live error trends data - no fallback to mock data
  const errorsByDay = errorTrends.map(trend => ({
    date: trend.date,
    errors: trend.errors,
    warnings: 0, // For now, we only track errors vs success, no separate warnings category
    total: trend.total,
    error_rate: trend.error_rate
  }));

  // Conditionally include Configuration tab only in Development mode
  const tabs = [
    { id: 'overview', label: 'Overview', icon: <Activity className="h-4 w-4" /> },
    { id: 'tenants', label: 'Tenants', icon: <Users className="h-4 w-4" /> },
    ...(isDevelopmentMode ? [{ id: 'config', label: 'Configuration', icon: <Settings className="h-4 w-4" /> }] : []),
    { id: 'system', label: 'System Health', icon: <Server className="h-4 w-4" /> },
    { id: 'logs', label: 'Error Logs', icon: <AlertTriangle className="h-4 w-4" /> }
  ];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Admin Panel</h1>
            <p className="text-gray-600 dark:text-gray-400">System monitoring and tenant management</p>
          </div>

          <div className="flex items-center gap-4">
            {/* Development/Production Mode Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Mode:</span>
              <button
                onClick={toggleDevelopmentMode}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${isDevelopmentMode
                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                  : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}
              >
                {isDevelopmentMode ? '🔧 Development' : '🏢 Production'}
              </button>
            </div>

            {/* Connection Status */}
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>

            {/* Refresh Button */}
            <button
              onClick={fetchAdminData}
              disabled={isLoading}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* System Status Alert */}
        <div className={`p-4 rounded-lg border ${isConnected && systemHealth && systemHealth.processing_enabled && systemHealth.error_rate < 10
          ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900 dark:border-green-700 dark:text-green-200'
          : isConnected && systemHealth && systemHealth.error_rate < 20
            ? 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900 dark:border-yellow-700 dark:text-yellow-200'
            : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900 dark:border-red-700 dark:text-red-200'
          }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {isConnected && systemHealth && systemHealth.processing_enabled && systemHealth.error_rate < 10 ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : isConnected && systemHealth && systemHealth.error_rate < 20 ? (
                <AlertTriangle className="h-5 w-5 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 mr-2" />
              )}
              <span className="font-medium">
                System Status: {
                  isConnected && systemHealth && systemHealth.processing_enabled && systemHealth.error_rate < 10
                    ? 'Healthy'
                    : isConnected && systemHealth && systemHealth.error_rate < 20
                      ? 'Warning'
                      : 'Critical'
                }
              </span>
            </div>
            <div className="text-sm">
              {systemHealth && (
                <span>
                  {systemHealth.documents_processed_today} docs today • {systemHealth.error_rate.toFixed(1)}% error rate
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* System Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {isLoading ? (
                [...Array(6)].map((_, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between animate-pulse">
                      <div className="flex-1">
                        <div className="w-20 h-4 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                        <div className="w-16 h-8 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                        <div className="w-12 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                      </div>
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-lg"></div>
                    </div>
                  </Card>
                ))
              ) : systemMetrics.length > 0 ? (
                systemMetrics.map((metric, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{metric.title}</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">{metric.value}</p>
                        <p className={`text-sm ${metric.status === 'healthy' ? 'text-green-600' :
                          metric.status === 'warning' ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                          {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                        </p>
                      </div>
                      <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        {metric.icon}
                      </div>
                    </div>
                  </Card>
                ))
              ) : (
                <div className="col-span-full">
                  <Card className="p-8">
                    <div className="text-center">
                      <Server className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <div>
                        <p className="text-gray-500 dark:text-gray-400 font-medium">System metrics unavailable</p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                          {isDevelopmentMode
                            ? 'Check if the backend service is running'
                            : 'Unable to retrieve system health data'}
                        </p>
                      </div>
                    </div>
                  </Card>
                </div>
              )}
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card
                title="Tenant Activity"
                description={`Document processing by tenant ${isDevelopmentMode ? '(Development)' : '(Production)'} ${isConnected ? '(Live Data)' : '(Connecting...)'}`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="flex flex-col items-center space-y-3">
                      <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                      <p className="text-gray-500 dark:text-gray-400">Loading tenant activity...</p>
                    </div>
                  </div>
                ) : tenantActivityData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={tenantActivityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="documents" fill="#3B82F6" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="flex flex-col items-center space-y-3 text-center">
                      <Users className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <div>
                        <p className="text-gray-500 dark:text-gray-400 font-medium">No tenant activity</p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                          {isDevelopmentMode
                            ? 'No development tenants found or no activity yet'
                            : 'No active tenants or recent activity'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </Card>

              <Card
                title="Error Trends"
                description={`Errors and warnings over time ${isDevelopmentMode ? '(Development)' : '(Production)'} ${isConnected ? '(Live Data)' : '(Connecting...)'}`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="flex flex-col items-center space-y-3">
                      <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                      <p className="text-gray-500 dark:text-gray-400">Loading error trends...</p>
                    </div>
                  </div>
                ) : errorsByDay.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={errorsByDay}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                      <YAxis />
                      <Tooltip labelFormatter={(value) => new Date(value).toLocaleDateString()} />
                      <Line type="monotone" dataKey="errors" stroke="#EF4444" strokeWidth={2} />
                      <Line type="monotone" dataKey="warnings" stroke="#F59E0B" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="flex flex-col items-center space-y-3 text-center">
                      <AlertTriangle className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <div>
                        <p className="text-gray-500 dark:text-gray-400 font-medium">No error trends data</p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                          {isDevelopmentMode
                            ? 'Process some documents to see error trends'
                            : 'No error data available for the selected period'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'tenants' && (
          <Card title="Tenant Management" description="Manage customer accounts and subscriptions">
            {tenantActivity.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Mail className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 font-medium">No tenants found</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                    {isDevelopmentMode
                      ? 'Development mode shows only test accounts'
                      : 'No customer tenants have been deployed yet'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Tenant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Plan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Documents
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Last Active
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {tenantActivity.map((tenant, index) => (
                      <tr key={tenant.tenant_name} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {tenant.tenant_name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {tenant.mailbox_count} mailboxes
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${tenant.storage_type === 'azure'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                            }`}>
                            {tenant.storage_type.toUpperCase()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${tenant.status === 'active'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                            {tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {tenant.documents_this_week.toLocaleString()}
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {tenant.success_rate.toFixed(1)}% success rate
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {tenant.last_activity ? new Date(tenant.last_activity).toLocaleDateString() : 'Never'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                            View
                          </button>
                          <button
                            onClick={() => {
                              setActiveTab('config');
                              toast.success('Switched to Configuration tab');
                            }}
                            className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                            title="Go to Configuration"
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Card>
        )}

        {activeTab === 'config' && (
          <div className="space-y-6">
            <Card title="Configuration Management" description="Edit system configuration for development environment">
              <div className="space-y-6">
                {/* Mailboxes Configuration */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Mailboxes</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Configure which mailboxes to monitor for incoming documents.
                    </p>
                    <div className="space-y-3">
                      {configData?.mailboxes && Object.keys(configData.mailboxes).length > 0 ? (
                        Object.entries(configData.mailboxes).map(([email, config]: [string, any]) => (
                          <div key={email} className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded-lg border">
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">{email}</div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {config.display_name || 'No display name'}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.enabled
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                }`}>
                                {config.enabled ? 'Enabled' : 'Disabled'}
                              </span>
                              <button
                                onClick={() => {
                                  setEditingConfigSection('mailbox');
                                  setEditingConfig({
                                    mailboxes: {
                                      ...configData?.mailboxes,
                                      [email]: { ...config }
                                    }
                                  });
                                  setShowEditConfigModal(true);
                                }}
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                                title="Edit mailbox settings"
                              >
                                <Settings className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                          No mailboxes configured
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => setShowAddMailboxModal(true)}
                      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Mailbox</span>
                    </button>
                  </div>
                </div>

                {/* Document Types Configuration */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Document Types</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Configure document types, keywords, and processing settings.
                    </p>
                    <div className="space-y-3">
                      {configData?.document_types && Object.keys(configData.document_types).length > 0 ? (
                        Object.entries(configData.document_types).map(([docType, config]: [string, any]) => (
                          <div key={docType} className="p-3 bg-white dark:bg-gray-700 rounded-lg border">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900 dark:text-white capitalize">
                                  {docType.replace('_', ' ')}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                  Keywords: {config.keywords ? config.keywords.join(', ') : 'None'}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  Upload: {config.actions?.upload ? '✓' : '✗'} |
                                  Notify: {config.actions?.notify ? '✓' : '✗'}
                                  {config.storage?.subfolder_format && (
                                    <span className="ml-2">| Path: {config.storage.subfolder_format}</span>
                                  )}
                                </div>
                                {config.notification?.recipients && config.notification.recipients.length > 0 && (
                                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                    Recipients: {config.notification.recipients.map((r: any) => r.name || r.email).join(', ')}
                                  </div>
                                )}
                              </div>
                              <button
                                onClick={() => {
                                  setEditingConfigSection('document_type');
                                  setEditingConfig({
                                    document_type: docType,
                                    config: { ...config }
                                  });
                                  setShowEditConfigModal(true);
                                }}
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                              >
                                <Settings className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                          No document types configured
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => setShowAddDocTypeModal(true)}
                      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Document Type</span>
                    </button>
                  </div>
                </div>

                {/* Storage Configuration */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Storage Settings</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Configure where documents are stored and folder structure.
                    </p>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Default Subfolder Format
                        </label>
                        <input
                          type="text"
                          value="{doc_type}/{document_year}/{company_name}"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          readOnly
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notification Settings */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Settings</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Configure email notifications and preferred language.
                    </p>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Preferred Language
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                          <option value="English">English</option>
                          <option value="Swedish">Swedish</option>
                          <option value="Spanish">Spanish</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Default Settings Configuration */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Default Settings</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Configure default settings that apply to all document types unless overridden.
                    </p>
                    <div className="space-y-4">
                      {configData?.defaults && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Default Storage Path Format
                            </label>
                            <input
                              type="text"
                              value={configData.defaults.storage?.subfolder_format || ''}
                              readOnly
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white"
                              placeholder="{doc_type}/{document_year}/{company_name}"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Preferred Language
                            </label>
                            <select
                              value={configData.defaults.preferred_language || 'English'}
                              onChange={(e) => {
                                const updatedDefaults = {
                                  ...configData.defaults,
                                  preferred_language: e.target.value
                                };
                                setConfigData({
                                  ...configData,
                                  defaults: updatedDefaults
                                });
                              }}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                            >
                              <option value="English">English</option>
                              <option value="Swedish">Swedish</option>
                              <option value="German">German</option>
                              <option value="French">French</option>
                            </select>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                checked={configData.defaults.actions?.upload || false}
                                onChange={(e) => {
                                  const updatedDefaults = {
                                    ...configData.defaults,
                                    actions: {
                                      ...configData.defaults.actions,
                                      upload: e.target.checked
                                    }
                                  };
                                  setConfigData({
                                    ...configData,
                                    defaults: updatedDefaults
                                  });
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Default Upload Enabled
                              </label>
                            </div>
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                checked={configData.defaults.actions?.notify || false}
                                onChange={(e) => {
                                  const updatedDefaults = {
                                    ...configData.defaults,
                                    actions: {
                                      ...configData.defaults.actions,
                                      notify: e.target.checked
                                    }
                                  };
                                  setConfigData({
                                    ...configData,
                                    defaults: updatedDefaults
                                  });
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Default Notifications Enabled
                              </label>
                            </div>
                          </div>

                          {/* Default Notification Recipients - only show when notifications are enabled */}
                          {configData.defaults.actions?.notify && (
                            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                Default Notification Recipients
                              </h4>
                              <div className="space-y-2">
                                {(configData.defaults.notification?.recipients || []).map((recipient: NotificationRecipient, index: number) => (
                                  <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-600 rounded border">
                                    <div className="flex-1">
                                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {recipient.name}
                                      </span>
                                      <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                                        ({recipient.email})
                                      </span>
                                    </div>
                                    <button
                                      onClick={() => {
                                        const updatedRecipients = (configData.defaults.notification?.recipients || []).filter((_: NotificationRecipient, i: number) => i !== index);
                                        const updatedDefaults = {
                                          ...configData.defaults,
                                          notification: {
                                            ...configData.defaults.notification,
                                            recipients: updatedRecipients
                                          }
                                        };
                                        setConfigData({
                                          ...configData,
                                          defaults: updatedDefaults
                                        });
                                      }}
                                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </div>
                                ))}

                                {/* Add new recipient form */}
                                <div className="flex space-x-2">
                                  <input
                                    type="text"
                                    placeholder="Name"
                                    value={newRecipientName}
                                    onChange={(e) => setNewRecipientName(e.target.value)}
                                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                  />
                                  <input
                                    type="email"
                                    placeholder="Email"
                                    value={newRecipientEmail}
                                    onChange={(e) => setNewRecipientEmail(e.target.value)}
                                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                  />
                                  <button
                                    onClick={() => {
                                      if (newRecipientName.trim() && newRecipientEmail.trim()) {
                                        const newRecipient = {
                                          name: newRecipientName.trim(),
                                          email: newRecipientEmail.trim()
                                        };
                                        const updatedRecipients = [...(configData.defaults.notification?.recipients || []), newRecipient];
                                        const updatedDefaults = {
                                          ...configData.defaults,
                                          notification: {
                                            ...configData.defaults.notification,
                                            recipients: updatedRecipients
                                          }
                                        };
                                        setConfigData({
                                          ...configData,
                                          defaults: updatedDefaults
                                        });
                                        setNewRecipientName('');
                                        setNewRecipientEmail('');
                                      }
                                    }}
                                    disabled={!newRecipientName.trim() || !newRecipientEmail.trim()}
                                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                  >
                                    <Plus className="h-4 w-4" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={configData.defaults.email_filtering?.process_external_only || false}
                              onChange={(e) => {
                                const updatedDefaults = {
                                  ...configData.defaults,
                                  email_filtering: {
                                    ...configData.defaults.email_filtering,
                                    process_external_only: e.target.checked
                                  }
                                };
                                setConfigData({
                                  ...configData,
                                  defaults: updatedDefaults
                                });
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                              Process External Emails Only
                            </label>
                          </div>
                        </div>
                      )}
                      <button
                        onClick={() => {
                          setEditingConfigSection('defaults');
                          setEditingConfig({
                            defaults: { ...configData?.defaults }
                          });
                          setShowEditConfigModal(true);
                        }}
                        className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Edit Default Settings</span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Save Configuration Button */}
                <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium flex items-center space-x-2"
                    onClick={async () => {
                      try {
                        const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify(configData),
                        });

                        if (response.ok) {
                          toast.success('Configuration settings saved successfully');
                          fetchConfigData(); // Refresh to confirm changes
                        } else {
                          const error = await response.json();
                          toast.error(`Failed to save configuration: ${error.error}`);
                        }
                      } catch (error) {
                        console.error('Error saving configuration:', error);
                        toast.error('Failed to save configuration');
                      }
                    }}
                  >
                    <Settings className="h-4 w-4" />
                    <span>Save Configuration</span>
                  </button>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    Changes are automatically saved when you add new mailboxes or document types.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'system' && (
          <div className="space-y-6">
            <Card title="System Health Details" description="Detailed system monitoring">
              {systemHealth ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Metrics</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">CPU Usage</span>
                        <span className="text-sm font-medium">{systemHealth.cpu_usage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${systemHealth.cpu_usage > 80 ? 'bg-red-600' : systemHealth.cpu_usage > 60 ? 'bg-yellow-600' : 'bg-blue-600'}`}
                          style={{ width: `${Math.min(systemHealth.cpu_usage, 100)}%` }}
                        />
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Memory Usage</span>
                        <span className="text-sm font-medium">{systemHealth.memory_usage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${systemHealth.memory_usage > 80 ? 'bg-red-600' : systemHealth.memory_usage > 60 ? 'bg-yellow-600' : 'bg-green-600'}`}
                          style={{ width: `${Math.min(systemHealth.memory_usage, 100)}%` }}
                        />
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Disk Usage</span>
                        <span className="text-sm font-medium">{systemHealth.disk_usage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${systemHealth.disk_usage > 80 ? 'bg-red-600' : systemHealth.disk_usage > 60 ? 'bg-yellow-600' : 'bg-orange-600'}`}
                          style={{ width: `${Math.min(systemHealth.disk_usage, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Service Status</h4>
                    <div className="space-y-3">
                      {(() => {
                        const getServiceStatus = (serviceName: string) => {
                          if (!systemHealth) return 'unknown';

                          switch (serviceName) {
                            case 'API Gateway':
                              return isConnected ? 'operational' : 'down';
                            case 'Document Processor':
                              return systemHealth.processing_enabled ? 'operational' : 'degraded';
                            case 'Email Service':
                              return systemHealth.processing_enabled && systemHealth.error_rate < 20 ? 'operational' : 'degraded';
                            case 'Database':
                              return systemHealth.error_rate < 10 ? 'operational' : 'degraded';
                            case 'File Storage':
                              return systemHealth.disk_usage < 90 ? 'operational' : 'degraded';
                            default:
                              return 'operational';
                          }
                        };

                        const services = [
                          'API Gateway',
                          'Document Processor',
                          'Email Service',
                          'Database',
                          'File Storage'
                        ];

                        return services.map((serviceName, index) => {
                          const status = getServiceStatus(serviceName);
                          return (
                            <div key={index} className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">{serviceName}</span>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${status === 'operational'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : status === 'degraded'
                                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                }`}>
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                              </span>
                            </div>
                          );
                        });
                      })()}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px]">
                  <div className="flex flex-col items-center space-y-3">
                    <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                    <p className="text-gray-500 dark:text-gray-400">Loading system health...</p>
                  </div>
                </div>
              )}
            </Card>
          </div>
        )}

        {activeTab === 'logs' && (
          <Card
            title="Error Logs"
            description={`System errors and warnings ${isDevelopmentMode ? '(Development)' : '(Production)'} ${isConnected ? '(Live Data)' : '(Connecting...)'}`}
          >
            <div className="space-y-4">
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="p-4 rounded-lg border-l-4 border-gray-200 bg-gray-50 dark:bg-gray-700 animate-pulse">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="w-12 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                            <div className="w-16 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                          </div>
                          <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-600 rounded mb-1"></div>
                          <div className="w-1/2 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        </div>
                        <div className="w-12 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : errorLogs.length > 0 ? (
                errorLogs.map((log) => (
                  <div key={log.id} className={`p-4 rounded-lg border-l-4 ${log.severity === 'error'
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                    }`}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full uppercase ${log.severity === 'error'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            }`}>
                            {log.severity}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {log.tenant}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            • {log.document_type}
                          </span>
                        </div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                          {log.error_message}
                        </p>
                        {log.filename && (
                          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                            File: {log.filename} • Mailbox: {log.mailbox}
                          </p>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400 font-medium">No error logs</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                      {isDevelopmentMode
                        ? 'No errors have occurred in development mode'
                        : 'System is running smoothly with no recent errors'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>

      {/* Add Mailbox Modal */}
      {showAddMailboxModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Add New Mailbox</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={newMailbox.email}
                  onChange={(e) => setNewMailbox({ ...newMailbox, email: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  value={newMailbox.displayName}
                  onChange={(e) => setNewMailbox({ ...newMailbox, displayName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Mailbox Display Name"
                />
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newMailbox.enabled}
                    onChange={(e) => setNewMailbox({ ...newMailbox, enabled: e.target.checked })}
                    className="form-checkbox h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable mailbox</span>
                </label>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddMailboxModal(false)}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleAddMailbox}
                disabled={!newMailbox.email}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Mailbox
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Document Type Modal */}
      {showAddDocTypeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Add New Document Type</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Document Type Name
                </label>
                <input
                  type="text"
                  value={newDocType.name}
                  onChange={(e) => setNewDocType({ ...newDocType, name: e.target.value, storagePath: e.target.value ? `${e.target.value}/{document_year}/{company_name}` : '' })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., contract, receipt, report"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Keywords (comma-separated)
                </label>
                <input
                  type="text"
                  value={newDocType.keywords}
                  onChange={(e) => setNewDocType({ ...newDocType, keywords: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="keyword1, keyword2, keyword3"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newDocType.upload}
                    onChange={(e) => setNewDocType({ ...newDocType, upload: e.target.checked })}
                    className="form-checkbox h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Upload documents</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newDocType.notify}
                    onChange={(e) => setNewDocType({ ...newDocType, notify: e.target.checked })}
                    className="form-checkbox h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Send notifications</span>
                </label>
              </div>

              {/* Storage Configuration - shown when upload is enabled */}
              {newDocType.upload && (
                <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">Storage Configuration</h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Storage Path
                    </label>
                    <input
                      type="text"
                      value={newDocType.storagePath}
                      onChange={(e) => setNewDocType({ ...newDocType, storagePath: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      placeholder="{doc_type}/{document_year}/{company_name}"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Variables: {'{doc_type}'}, {'{document_year}'}, {'{company_name}'}
                    </p>
                    {newDocType.storagePath && (
                      <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <p className="text-xs font-medium text-gray-700 dark:text-gray-300">Preview:</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400 font-mono">
                          📁 {newDocType.storagePath.replace('{doc_type}', newDocType.name || 'DocumentType').replace('{document_year}', '2024').replace('{company_name}', 'Acme Corp')}/
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Notification Configuration - shown when notify is enabled */}
              {newDocType.notify && (
                <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">Notification Recipients</h4>
                  <div className="space-y-3">
                    {newDocType.recipients.map((recipient, index) => (
                      <div key={index} className="flex space-x-2">
                        <input
                          type="text"
                          value={recipient.name}
                          onChange={(e) => {
                            const updatedRecipients = [...newDocType.recipients];
                            updatedRecipients[index].name = e.target.value;
                            setNewDocType({ ...newDocType, recipients: updatedRecipients });
                          }}
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Recipient name"
                        />
                        <input
                          type="email"
                          value={recipient.email}
                          onChange={(e) => {
                            const updatedRecipients = [...newDocType.recipients];
                            updatedRecipients[index].email = e.target.value;
                            setNewDocType({ ...newDocType, recipients: updatedRecipients });
                          }}
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="<EMAIL>"
                        />
                        {newDocType.recipients.length > 1 && (
                          <button
                            onClick={() => {
                              const updatedRecipients = newDocType.recipients.filter((_, i) => i !== index);
                              setNewDocType({ ...newDocType, recipients: updatedRecipients });
                            }}
                            className="px-3 py-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          >
                            ✕
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      onClick={() => {
                        setNewDocType({
                          ...newDocType,
                          recipients: [...newDocType.recipients, { name: '', email: '' }]
                        });
                      }}
                      className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      + Add another recipient
                    </button>
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddDocTypeModal(false)}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleAddDocType}
                disabled={!newDocType.name}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Document Type
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Edit Modal */}
      {showEditConfigModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Edit Configuration - {editingConfigSection}
            </h3>

            {editingConfigSection === 'mailbox' && editingConfig && (
              <div className="space-y-4">
                {Object.entries(editingConfig.mailboxes || {}).map(([email, config]: [string, any]) => (
                  <div key={email} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">{email}</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Display Name
                        </label>
                        <input
                          type="text"
                          value={config.display_name || ''}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              mailboxes: {
                                ...editingConfig.mailboxes,
                                [email]: { ...config, display_name: e.target.value }
                              }
                            });
                          }}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter display name"
                        />
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={config.enabled || false}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              mailboxes: {
                                ...editingConfig.mailboxes,
                                [email]: { ...config, enabled: e.target.checked }
                              }
                            });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Enabled
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {editingConfigSection === 'defaults' && editingConfig && (
              <div className="space-y-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Default Settings</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Storage Path Format
                      </label>
                      <input
                        type="text"
                        value={editingConfig.defaults?.storage?.subfolder_format || ''}
                        onChange={(e) => {
                          setEditingConfig({
                            ...editingConfig,
                            defaults: {
                              ...editingConfig.defaults,
                              storage: {
                                ...editingConfig.defaults?.storage,
                                subfolder_format: e.target.value
                              }
                            }
                          });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                        placeholder="{doc_type}/{document_year}/{company_name}"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Preferred Language
                      </label>
                      <select
                        value={editingConfig.defaults?.preferred_language || 'English'}
                        onChange={(e) => {
                          setEditingConfig({
                            ...editingConfig,
                            defaults: {
                              ...editingConfig.defaults,
                              preferred_language: e.target.value
                            }
                          });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="English">English</option>
                        <option value="Swedish">Swedish</option>
                        <option value="German">German</option>
                        <option value="French">French</option>
                      </select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editingConfig.defaults?.actions?.upload || false}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              defaults: {
                                ...editingConfig.defaults,
                                actions: {
                                  ...editingConfig.defaults?.actions,
                                  upload: e.target.checked
                                }
                              }
                            });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Default Upload Enabled
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editingConfig.defaults?.actions?.notify || false}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              defaults: {
                                ...editingConfig.defaults,
                                actions: {
                                  ...editingConfig.defaults?.actions,
                                  notify: e.target.checked
                                }
                              }
                            });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Default Notifications Enabled
                        </label>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editingConfig.defaults?.email_filtering?.process_external_only || false}
                        onChange={(e) => {
                          setEditingConfig({
                            ...editingConfig,
                            defaults: {
                              ...editingConfig.defaults,
                              email_filtering: {
                                ...editingConfig.defaults?.email_filtering,
                                process_external_only: e.target.checked
                              }
                            }
                          });
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        Process External Emails Only
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {editingConfigSection === 'document_type' && editingConfig && (
              <div className="space-y-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Edit Document Type: {editingConfig.document_type?.replace('_', ' ').toUpperCase()}
                  </h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Keywords (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={editingConfig.config?.keywords?.join(', ') || ''}
                        onChange={(e) => {
                          const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                          setEditingConfig({
                            ...editingConfig,
                            config: {
                              ...editingConfig.config,
                              keywords: keywords
                            }
                          });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                        placeholder="invoice, bill, receipt"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Storage Path Format
                      </label>
                      <input
                        type="text"
                        value={editingConfig.config?.storage?.subfolder_format || ''}
                        onChange={(e) => {
                          setEditingConfig({
                            ...editingConfig,
                            config: {
                              ...editingConfig.config,
                              storage: {
                                ...editingConfig.config?.storage,
                                subfolder_format: e.target.value
                              }
                            }
                          });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                        placeholder="{doc_type}/{document_year}/{company_name}"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editingConfig.config?.actions?.upload || false}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              config: {
                                ...editingConfig.config,
                                actions: {
                                  ...editingConfig.config?.actions,
                                  upload: e.target.checked
                                }
                              }
                            });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Enable Upload
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editingConfig.config?.actions?.notify || false}
                          onChange={(e) => {
                            setEditingConfig({
                              ...editingConfig,
                              config: {
                                ...editingConfig.config,
                                actions: {
                                  ...editingConfig.config?.actions,
                                  notify: e.target.checked
                                }
                              }
                            });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Enable Notifications
                        </label>
                      </div>
                    </div>
                    {editingConfig.config?.actions?.notify && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Notification Recipients (comma-separated emails)
                        </label>
                        <input
                          type="text"
                          value={editingConfig.config?.notification?.recipients?.map((r: any) => r.email || r).join(', ') || ''}
                          onChange={(e) => {
                            const emails = e.target.value.split(',').map(email => email.trim()).filter(email => email);
                            const recipients = emails.map(email => ({ email, name: email.split('@')[0] }));
                            setEditingConfig({
                              ...editingConfig,
                              config: {
                                ...editingConfig.config,
                                notification: {
                                  ...editingConfig.config?.notification,
                                  recipients: recipients
                                }
                              }
                            });
                          }}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="<EMAIL>, <EMAIL>"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditConfigModal(false);
                  setEditingConfig(null);
                  setEditingConfigSection('');
                }}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveConfigChanges}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default AdminPanel;